import requests
import jwt
import json
from datetime import datetime

class TryGCPTester:
    def __init__(self, event_id):
        self.event_id = event_id
        self.base_url = "https://trygcp.dev"
        
    def test_direct_api_call(self):
        """测试直接API调用"""
        url = f"{self.base_url}/api/events/{self.event_id}/assign"
        
        # 测试无认证请求
        response = requests.post(url)
        print(f"无认证响应: {response.status_code}")
        
        # 测试伪造认证头
        fake_headers = {
            'Authorization': 'Bearer fake_token',
            'Content-Type': 'application/json'
        }
        response = requests.post(url, headers=fake_headers)
        print(f"伪造认证响应: {response.status_code}")
        
    def analyze_auth_requirements(self):
        """分析认证要求"""
        # 获取事件信息
        event_url = f"{self.base_url}/api/events/{self.event_id}"
        response = requests.get(event_url)
        
        if response.status_code == 200:
            print("事件信息可公开访问")
            print(json.dumps(response.json(), indent=2))
        else:
            print(f"事件信息需要认证: {response.status_code}")
    
    def test_session_analysis(self):
        """会话分析测试"""
        session = requests.Session()
        
        # 尝试访问主页
        response = session.get(self.base_url)
        print(f"主页访问: {response.status_code}")
        
        # 检查是否设置了cookies
        print(f"Cookies: {session.cookies}")
        
        # 尝试访问用户API
        user_response = session.get(f"{self.base_url}/api/user")
        print(f"用户API响应: {user_response.status_code}")

# 使用示例
tester = TryGCPTester("your_event_id")
tester.test_direct_api_call()
tester.analyze_auth_requirements()